<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <style>
    /* Color Palette */
    .bg-color { fill: #F8FAFC; }
    .primary-color { fill: #1E40AF; }
    .secondary-color { fill: #475569; }
    .accent-color { fill: #3B82F6; }
    .text-primary { fill: #1E293B; }
    .text-secondary { fill: #64748B; }
    .text-light { fill: #94A3B8; }
    .card-bg { fill: #FFFFFF; }
    .card-border { stroke: #BAE6FD; }
    .container-bg { fill: #E0F2FE; }

    /* Gradients */
    /* Note: SVG linearGradient takes x1, y1, x2, y2 as percentages or user space units */
    /* For objectBoundingBox, 0 is start, 1 is end */
    .gradient-primary-fill {
      fill: url(#PrimaryGradient);
    }
    .gradient-accent-fill {
      fill: url(#AccentGradient);
    }
    .gradient-light-accent-fill {
      fill: url(#LightAccentGradient);
    }

    /* Font System */
    /* Using specific font names, ensuring fallbacks */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* Font Sizes and Weights */
    .hero-title { font-size: 72px; font-weight: 700; fill: #1E293B; }
    .main-title { font-size: 56px; font-weight: 700; fill: #1E293B; }
    .section-title { font-size: 36px; font-weight: 700; fill: #1E293B; }
    .content-title { font-size: 28px; font-weight: 600; fill: #1E293B; }
    .body-text { font-size: 22px; font-weight: 400; fill: #64748B; line-height: 1.4; } /* line-height not directly supported on text element, handled by dy */
    .small-text { font-size: 16px; font-weight: 400; fill: #64748B; }
    .caption { font-size: 14px; font-weight: 400; fill: #94A3B8; }
    .number-large { font-size: 96px; font-weight: 700; fill: #1E40AF; } /* Custom large number for emphasis */
    .number-medium { font-size: 48px; font-weight: 700; fill: #1E40AF; }

    /* Card Style */
    .card {
      filter: url(#cardShadow); /* Apply shadow filter */
      stroke: #BAE6FD; /* Border color */
      stroke-width: 1px;
    }

    /* Icon Style (Outline) */
    .icon {
      stroke: #3B82F6; /* Accent color for icons */
      stroke-width: 2px;
      fill: none; /* No fill for outline icons */
    }

    /* Chart Specific Styles */
    .chart-label-x { font-size: 16px; fill: #64748B; text-anchor: middle; }
    .chart-label-y { font-size: 16px; fill: #64748B; text-anchor: end; }
    .chart-data-label { font-size: 18px; font-weight: 600; fill: #1E293B; text-anchor: middle; }
    .chart-axis-line { stroke: #E0F2FE; stroke-width: 2px; }
    .chart-bar { fill: #3B82F6; } /* Default bar color */
    .chart-bar-alt { fill: #1E40AF; } /* Alternate bar color */
    .chart-line { stroke: #3B82F6; stroke-width: 4px; fill: none; }
    .chart-point { fill: #3B82F6; stroke-width: 0; }
  </style>

  <defs>
    <!-- Gradients -->
    <linearGradient id="PrimaryGradient" x1="0" y1="0" x2="1" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>
    <linearGradient id="AccentGradient" x1="0" y1="0" x2="1" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <!-- Highlight color itself with transparency gradient for tech feel -->
    <linearGradient id="LightAccentGradient" x1="0" y1="0" x2="1" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.2" />
      <stop offset="100%" stop-color="#1E40AF" stop-opacity="0.05" />
    </linearGradient>

    <!-- Shadow filter for cards -->
    <filter id="cardShadow" x="-5%" y="-5%" width="110%" height="110%">
      <feOffset dx="0" dy="4" />
      <feGaussianBlur stdDeviation="6" result="offset-blur" />
      <feComposite operator="out" in="SourceGraphic" in2="offset-blur" result="inverse-composite" />
      <feFlood flood-color="rgba(0,0,0,0.1)" flood-opacity="1" result="color" />
      <feComposite operator="in" in="color" in2="inverse-composite" result="shadow" />
      <feComposite operator="over" in="shadow" in2="SourceGraphic" />
    </filter>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- Decorative Gradient Overlay -->
  <rect x="0" y="0" width="1920" height="1080" class="gradient-light-accent-fill" opacity="0.1" />

  <!-- Page Header -->
  <g class="font-primary">
    <!-- Logo Placeholder (Top Left) -->
    <rect x="80" y="60" width="160" height="40" rx="8" class="accent-color" />
    <text x="160" y="87" text-anchor="middle" class="content-title" fill="#FFFFFF">Logo</text>

    <!-- Page Title (Centered) -->
    <text x="960" y="90" text-anchor="middle" class="main-title">
      <tspan class="text-primary">商业计划书</tspan>
      <tspan x="960" dy="60" class="body-text">Business Plan Presentation</tspan>
    </text>

    <!-- Page Number (Top Right) -->
    <text x="1760" y="90" text-anchor="end" class="section-title text-secondary">6/10</text>
  </g>

  <!-- Main Content Area - Bento Grid Style Layout -->
  <!-- Using transform to apply overall margins to the content block -->
  <g transform="translate(80, 160)">
    <!-- Left Section: Data Charts (Large Block) -->
    <g>
      <rect x="0" y="0" width="1000" height="800" rx="12" class="card-bg card" />

      <text x="500" y="50" text-anchor="middle" class="section-title text-primary font-primary">市场增长趋势</text>
      <text x="500" y="90" text-anchor="middle" class="small-text font-secondary">Market Growth Trend Analysis</text>

      <!-- Bar Chart Simulation -->
      <g transform="translate(100, 180)"> <!-- Adjusted Y for spacing -->
        <text x="0" y="-30" class="content-title text-primary font-primary">行业营收对比</text>
        <text x="0" y="0" class="small-text text-secondary font-secondary">Industry Revenue Comparison</text>

        <!-- Y-axis labels -->
        <text x="-20" y="200" class="chart-label-y">200亿</text>
        <text x="-20" y="100" class="chart-label-y">400亿</text>
        <text x="-20" y="0" class="chart-label-y">600亿</text>
        <line x1="0" y1="220" x2="0" y2="0" class="chart-axis-line" />
        <line x1="0" y1="220" x2="700" y2="220" class="chart-axis-line" />

        <!-- Bars and Data Labels (ensure spacing) -->
        <rect x="50" y="120" width="80" height="100" rx="4" class="chart-bar" />
        <text x="90" y="110" class="chart-data-label">350</text>
        <text x="90" y="245" class="chart-label-x">2021</text>

        <rect x="180" y="80" width="80" height="140" rx="4" class="chart-bar-alt" />
        <text x="220" y="70" class="chart-data-label">480</text>
        <text x="220" y="245" class="chart-label-x">2022</text>

        <rect x="310" y="40" width="80" height="180" rx="4" class="chart-bar" />
        <text x="350" y="30" class="chart-data-label">620</text>
        <text x="350" y="245" class="chart-label-x">2023</text>

        <rect x="440" y="0" width="80" height="220" rx="4" class="chart-bar-alt" />
        <text x="480" y="-10" class="chart-data-label">750</text>
        <text x="480" y="245" class="chart-label-x">2024E</text>
      </g>

      <!-- Line Chart Simulation -->
      <g transform="translate(100, 560)"> <!-- Adjusted Y for spacing -->
        <text x="0" y="-30" class="content-title text-primary font-primary">用户增长预测</text>
        <text x="0" y="0" class="small-text text-secondary font-secondary">User Growth Projection</text>

        <!-- Y-axis labels -->
        <text x="-20" y="160" class="chart-label-y">0</text>
        <text x="-20" y="100" class="chart-label-y">500万</text>
        <text x="-20" y="40" class="chart-label-y">1000万</text>
        <line x1="0" y1="180" x2="0" y2="0" class="chart-axis-line" />
        <line x1="0" y1="180" x2="700" y2="180" class="chart-axis-line" />

        <!-- Polyline for line chart -->
        <polyline points="50,150 180,120 310,80 440,30" class="chart-line" />

        <!-- Data points -->
        <circle cx="50" cy="150" r="6" class="chart-point" />
        <circle cx="180" cy="120" r="6" class="chart-point" />
        <circle cx="310" cy="80" r="6" class="chart-point" />
        <circle cx="440" cy="30" r="6" class="chart-point" />

        <!-- Data labels (ensure no overlap) -->
        <text x="50" y="195" class="chart-label-x">Q1</text>
        <text x="50" y="140" class="chart-data-label">2M</text>

        <text x="180" y="195" class="chart-label-x">Q2</text>
        <text x="180" y="110" class="chart-data-label">4M</text>

        <text x="310" y="195" class="chart-label-x">Q3</text>
        <text x="310" y="70" class="chart-data-label">7M</text>

        <text x="440" y="195" class="chart-label-x">Q4</text>
        <text x="440" y="20" class="chart-data-label">10M</text>
      </g>
    </g>

    <!-- Right Section: Key Metrics & Data Cards (Bento Grid) -->
    <g transform="translate(1040, 0)"> <!-- 1000 (chart width) + 40 (gap) = 1040 -->
      <!-- Top Card: Large Number Highlight -->
      <rect x="0" y="0" width="720" height="380" rx="12" class="card-bg card" />
      <text x="360" y="100" text-anchor="middle" class="content-title text-primary font-primary">
        <tspan>市场占有率</tspan>
        <tspan x="360" dy="40" class="small-text text-secondary font-secondary">Market Share Projection</tspan>
      </text>
      <!-- Super large number for emphasis -->
      <text x="360" y="260" text-anchor="middle" class="number-large font-primary accent-color">25.3%</text>
      <text x="360" y="320" text-anchor="middle" class="body-text text-secondary font-secondary">
        <tspan>预计未来三年增长</tspan>
        <tspan x="360" dy="30">Projected Growth in Next 3 Years</tspan>
      </text>

      <!-- Bottom Cards: Data Statistics (Side-by-side) -->
      <g transform="translate(0, 420)"> <!-- 380 (top card height) + 40 (gap) = 420 -->
        <!-- Card 1: Investment Return -->
        <rect x="0" y="0" width="340" height="380" rx="12" class="card-bg card" />
        <rect x="0" y="0" width="340" height="10" rx="12" class="accent-color" /> <!-- Top accent bar -->
        <text x="170" y="80" text-anchor="middle" class="content-title text-primary font-primary">
          <tspan>投资回报率</tspan>
          <tspan x="170" dy="40" class="small-text text-secondary font-secondary">Return on Investment</tspan>
        </text>
        <text x="170" y="220" text-anchor="middle" class="number-medium font-primary primary-color">32.5%</text>
        <text x="170" y="270" text-anchor="middle" class="body-text text-secondary font-secondary">
          <tspan>预期年度增长</tspan>
          <tspan x="170" dy="30">Expected Annual Growth</tspan>
        </text>
        <!-- Icon: Simple Money Bag/Growth (Outline) -->
        <g class="icon" transform="translate(170, 325)">
          <path d="M-20 -10 L20 -10 L25 5 L-25 5 Z" />
          <path d="M-10 0 L-10 10 M10 0 L10 10" />
          <path d="M0 -15 L0 15 M-15 0 L15 0" />
        </g>

        <!-- Card 2: Risk Assessment -->
        <rect x="380" y="0" width="340" height="380" rx="12" class="card-bg card" /> <!-- 340 (card width) + 40 (gap) = 380 -->
        <rect x="380" y="0" width="340" height="10" rx="12" class="primary-color" /> <!-- Top accent bar -->
        <text x="550" y="80" text-anchor="middle" class="content-title text-primary font-primary">
          <tspan>风险评估</tspan>
          <tspan x="550" dy="40" class="small-text text-secondary font-secondary">Risk Assessment Score</tspan>
        </text>
        <text x="550" y="220" text-anchor="middle" class="number-medium font-primary primary-color">低</text>
        <text x="550" y="270" text-anchor="middle" class="body-text text-secondary font-secondary">
          <tspan>综合风险等级</tspan>
          <tspan x="550" dy="30">Overall Risk Level</tspan>
        </text>
        <!-- Icon: Simple Shield (Outline) -->
        <g class="icon" transform="translate(550, 325)">
          <path d="M-20 0 L0 -15 L20 0 L20 15 L-20 15 Z" />
          <line x1="0" y1="-5" x2="0" y2="5" />
          <line x1="-5" y1="0" x2="5" y2="0" />
        </g>
      </g>
    </g>
  </g>

  <!-- Footer Information -->
  <g class="font-secondary">
    <text x="960" y="1020" text-anchor="middle" class="caption text-light">
      <tspan>了解更多信息，请联系我们</tspan>
      <tspan x="960" dy="20">For more information, please contact us.</tspan>
    </text>
  </g>

</svg>