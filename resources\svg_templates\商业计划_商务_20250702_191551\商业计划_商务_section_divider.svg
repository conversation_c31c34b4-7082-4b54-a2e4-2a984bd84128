<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style type="text/css">
      /* Color Palette */
      /* Backgrounds */
      .bg-primary { fill: #F8FAFC; } /* background_color */
      .bg-container { fill: #E0F2FE; } /* container_background */

      /* Primary, Secondary, Accent Colors */
      .color-primary { fill: #1E40AF; } /* primary_color */
      .color-secondary { fill: #475569; } /* secondary_color */
      .color-accent { fill: #3B82F6; } /* accent_color */

      /* Text Colors */
      .text-primary-color { fill: #1E293B; } /* text_primary */
      .text-secondary-color { fill: #64748B; } /* text_secondary */
      .text-light-color { fill: #94A3B8; } /* text_light */

      /* Borders and Strokes */
      .border-card { stroke: #BAE6FD; } /* card_border */

      /* Gradients Definitions */
      /* Primary Gradient: linear-gradient(135deg, #1E40AF, #475569) */
      .gradient-primary-fill { fill: url(#primaryGradient); }
      <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stop-color="#1E40AF"/>
        <stop offset="100%" stop-color="#475569"/>
      </linearGradient>

      /* Accent Gradient: linear-gradient(45deg, #3B82F6, #1E40AF) */
      .gradient-accent-fill { fill: url(#accentGradient); }
      <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stop-color="#3B82F6"/>
        <stop offset="100%" stop-color="#1E40AF"/>
      </linearGradient>

      /* Subtle Background Gradient: linear-gradient(180deg, #F8FAFC, #E0F2FE) */
      .gradient-background-fill { fill: url(#subtleBackgroundGradient); }
      <linearGradient id="subtleBackgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stop-color="#F8FAFC"/>
        <stop offset="100%" stop-color="#E0F2FE"/>
      </linearGradient>

      /* Accent Fade Gradient (for tech feel, no different highlight colors) */
      .gradient-accent-fade-fill { fill: url(#accentFadeGradient); }
      <linearGradient id="accentFadeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.8"/>
        <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.1"/>
      </linearGradient>

      /* Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }

      /* Text Styles */
      .chapter-number {
        font-size: 36px; /* section_title */
        font-weight: 700; /* bold */
        fill: #3B82F6; /* accent_color */
        letter-spacing: 0.05em; /* wider */
      }
      .main-title {
        font-size: 56px; /* main_title */
        font-weight: 700; /* bold */
        line-height: 1.4; /* normal */
        fill: #1E293B; /* text_primary */
      }
      .subtitle-text {
        font-size: 28px; /* content_title */
        font-weight: 400; /* normal */
        line-height: 1.4; /* normal */
        fill: #64748B; /* text_secondary */
      }
      .page-info-text {
        font-size: 16px; /* small_text */
        font-weight: 400; /* normal */
        fill: #64748B; /* text_secondary */
      }
      .logo-text {
        font-size: 22px; /* body_text */
        font-weight: 700; /* bold */
        fill: #1E40AF; /* primary_color */
      }
    </style>
  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" class="gradient-background-fill"/>

  <!-- Decorative Elements - Abstract Business Flow and Transition -->
  <!-- Large, sweeping shapes for visual impact and transition -->
  <rect x="-100" y="150" width="900" height="450" rx="40" ry="40" class="gradient-accent-fill" transform="rotate(-10 0 150)" opacity="0.6"/>
  <rect x="1100" y="400" width="900" height="450" rx="40" ry="40" class="gradient-primary-fill" transform="rotate(10 1200 400)" opacity="0.5"/>

  <!-- Smaller, sharper geometric accents -->
  <path d="M150 50 L 300 50 L 250 200 L 100 200 Z" class="color-primary" opacity="0.15" transform="translate(100 50)"/>
  <circle cx="1700" cy="100" r="100" class="color-accent" opacity="0.1"/>
  <rect x="1550" y="800" width="250" height="120" rx="20" ry="20" class="color-secondary" opacity="0.15"/>

  <!-- A subtle wave/divider element at the bottom for visual separation -->
  <path d="M0 750 C 350 700, 700 800, 960 750 S 1600 700, 1920 750 V 1080 H 0 Z" fill="#E0F2FE" opacity="0.8"/>

  <!-- Main Content Area - Centered for prominence -->
  <g id="chapterContent" transform="translate(960, 480)">
    <!-- Chapter Number (English, smaller, accent color) -->
    <text x="0" y="-120" text-anchor="middle" class="font-primary chapter-number">
      <tspan x="0" y="-120">CHAPTER 03</tspan>
    </text>

    <!-- Main Title (Chinese, large, bold, primary text color) -->
    <text x="0" y="-40" text-anchor="middle" class="font-primary main-title">
      <tspan x="0" y="-40">{title}</tspan>
    </text>

    <!-- Subtitle / Description (Mixed Chinese and English, secondary text color) -->
    <text x="0" y="40" text-anchor="middle" class="font-secondary subtitle-text">
      <tspan x="0" y="40">深入解析市场趋势和竞争格局</tspan>
      <tspan x="0" y="85">Market Analysis and Competitive Landscape</tspan>
    </text>

    <!-- Placeholder for a simple business icon (e.g., a growth chart or target) -->
    <g id="decorativeIcon" transform="translate(0, 200)">
      <rect x="-60" y="-80" width="120" height="80" rx="10" ry="10" fill="none" stroke="#3B82F6" stroke-width="3"/>
      <line x1="-40" y1="-50" x2="-10" y2="-70" stroke="#3B82F6" stroke-width="3" stroke-linecap="round"/>
      <line x1="-10" y1="-70" x2="20" y2="-20" stroke="#3B82F6" stroke-width="3" stroke-linecap="round"/>
      <line x1="20" y1="-20" x2="50" y2="-40" stroke="#3B82F6" stroke-width="3" stroke-linecap="round"/>
      <circle cx="50" cy="-40" r="8" fill="#3B82F6"/>
    </g>
  </g>

  <!-- Page Information (Bottom Right) -->
  <g id="pageInfo" transform="translate(1840, 1020)">
    <text x="0" y="0" text-anchor="end" class="font-secondary page-info-text">
      <tspan x="0" y="0">页面 3/10</tspan>
    </text>
  </g>

  <!-- Logo Placeholder (Top Left) -->
  <g id="logoPlaceholder" transform="translate(80, 60)">
    <rect x="0" y="0" width="180" height="50" rx="10" ry="10" fill="#FFFFFF" opacity="0.7" stroke="#BAE6FD" stroke-width="1"/>
    <text x="90" y="32" text-anchor="middle" class="font-primary logo-text">
      <tspan x="90" y="32">{logo_url}</tspan>
    </text>
  </g>

</svg>