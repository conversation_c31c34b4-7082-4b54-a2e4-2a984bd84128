<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义颜色、字体、阴影等CSS样式 -->
    <style type="text/css">
      /* 统一蓝色系配色方案 */
      .bg-color { fill: #F8FAFC; } /* 背景色 */
      .primary-color { fill: #1E40AF; } /* 主色 */
      .secondary-color { fill: #475569; } /* 辅助色 */
      .accent-color { fill: #3B82F6; } /* 强调色 */
      .text-primary-color { fill: #1E293B; } /* 主要文字色 */
      .text-secondary-color { fill: #64748B; } /* 辅助文字色 */
      .card-bg-color { fill: #FFFFFF; } /* 卡片背景色 */
      .card-border-color { stroke: #BAE6FD; } /* 卡片边框色 */
      .success-icon-color { fill: #10B981; } /* 成功图标色 (来自设计规范中的success_color) */

      /* 字体系统 */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }

      /* 文本样式定义 */
      .main-title {
        font-size: 56px; /* main_title */
        font-weight: 700; /* bold */
        fill: #1E293B; /* text_primary */
      }
      .section-title {
        font-size: 36px; /* section_title */
        font-weight: 700; /* bold */
        fill: #1E293B; /* text_primary */
      }
      .content-title {
        font-size: 28px; /* content_title */
        font-weight: 500; /* medium */
        fill: #475569; /* secondary_color */
      }
      .body-text {
        font-size: 22px; /* body_text */
        font-weight: 400; /* normal */
        fill: #64748B; /* text_secondary */
        /* 行高通过tspan的dy属性控制，确保不重叠 */
      }
      .small-text {
        font-size: 16px; /* small_text */
        font-weight: 400; /* normal */
        fill: #64748B; /* text_secondary */
      }
      .emphasis-text {
        font-size: 24px;
        font-weight: 600; /* semibold */
        fill: #1E40AF; /* primary_color */
      }
      .call-to-action-text {
        font-size: 32px;
        font-weight: 700; /* bold */
        fill: #3B82F6; /* accent_color */
      }

      /* 卡片阴影样式 */
      .card-shadow {
        filter: url(#drop-shadow);
      }
    </style>

    <!-- 阴影滤镜定义 -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 装饰性渐变背景 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>

    <!-- 列表项图标 (简单圆形) -->
    <circle id="bullet-icon" cx="0" cy="0" r="6" fill="#3B82F6"/>

    <!-- 行动要点图标 (简单勾线对勾) -->
    <path id="checkmark-icon" d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z" class="success-icon-color"/>

  </defs>

  <!-- 页面背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- 装饰性几何图形和渐变元素 -->
  <rect x="0" y="0" width="300" height="1080" fill="url(#primaryGradient)" opacity="0.05"/>
  <rect x="1620" y="0" width="300" height="1080" fill="url(#primaryGradient)" opacity="0.05"/>
  <circle cx="1750" cy="150" r="80" fill="#3B82F6" opacity="0.1"/>
  <circle cx="150" cy="950" r="120" fill="#1E40AF" opacity="0.08"/>

  <!-- 主标题 -->
  <text x="960" y="110" text-anchor="middle" class="font-primary main-title">{title}</text>

  <!-- 副标题 -->
  <text x="960" y="196" text-anchor="middle" class="font-secondary content-title">{subtitle}</text>

  <!-- 主要内容区域 -->
  <!-- 左列: 主要结论 -->
  <g class="font-secondary">
    <text x="80" y="280" class="section-title">主要结论</text>
    <text x="100" y="330" class="body-text">
      <tspan x="100" dy="0">
        <use href="#bullet-icon" x="-20" y="-8"/> {content_conclusion_1_zh} <tspan class="small-text">{content_conclusion_1_en}</tspan>
      </tspan>
      <tspan x="100" dy="40">
        <use href="#bullet-icon" x="-20" y="-8"/> {content_conclusion_2_zh} <tspan class="small-text">{content_conclusion_2_en}</tspan>
      </tspan>
      <tspan x="100" dy="40">
        <use href="#bullet-icon" x="-20" y="-8"/> {content_conclusion_3_zh} <tspan class="small-text">{content_conclusion_3_en}</tspan>
      </tspan>
      <tspan x="100" dy="40">
        <use href="#bullet-icon" x="-20" y="-8"/> {content_conclusion_4_zh} <tspan class="small-text">{content_conclusion_4_en}</tspan>
      </tspan>
      <tspan x="100" dy="40">
        <use href="#bullet-icon" x="-20" y="-8"/> {content_conclusion_5_zh} <tspan class="small-text">{content_conclusion_5_en}</tspan>
      </tspan>
    </text>
  </g>

  <!-- 右列: 行动要点 (卡片式设计) -->
  <g class="font-secondary">
    <text x="985" y="280" class="section-title">行动要点</text>

    <!-- 行动要点1 卡片 -->
    <rect x="985" y="320" width="855" height="100" rx="12" class="card-bg-color card-shadow" stroke="#BAE6FD" stroke-width="1"/>
    <text x="1025" y="360" class="emphasis-text">
      <use href="#checkmark-icon" x="-30" y="-12" width="24" height="24"/> {action_point_1_zh}
      <tspan x="1025" dy="30" class="small-text">{action_point_1_en}</tspan>
    </text>

    <!-- 行动要点2 卡片 -->
    <rect x="985" y="440" width="855" height="100" rx="12" class="card-bg-color card-shadow" stroke="#BAE6FD" stroke-width="1"/>
    <text x="1025" y="480" class="emphasis-text">
      <use href="#checkmark-icon" x="-30" y="-12" width="24" height="24"/> {action_point_2_zh}
      <tspan x="1025" dy="30" class="small-text">{action_point_2_en}</tspan>
    </text>

    <!-- 行动要点3 卡片 -->
    <rect x="985" y="560" width="855" height="100" rx="12" class="card-bg-color card-shadow" stroke="#BAE6FD" stroke-width="1"/>
    <text x="1025" y="600" class="emphasis-text">
      <use href="#checkmark-icon" x="-30" y="-12" width="24" height="24"/> {action_point_3_zh}
      <tspan x="1025" dy="30" class="small-text">{action_point_3_en}</tspan>
    </text>
  </g>

  <!-- 底部区域 -->
  <g class="font-secondary">
    <!-- 左侧: 联系信息 -->
    <text x="80" y="750" class="section-title">联系我们</text>
    <text x="80" y="800" class="body-text">
      <tspan x="80" dy="0">邮箱: {contact_email}</tspan>
      <tspan x="80" dy="40">电话: {contact_phone}</tspan>
      <tspan x="80" dy="40">网址: {contact_website}</tspan>
    </text>

    <!-- 右侧: 感谢语和行动号召 -->
    <text x="985" y="750" class="section-title">感谢您的关注</text>
    <text x="985" y="800" class="body-text">
      <tspan x="985" dy="0">期待与您携手共创未来。</tspan>
      <tspan x="985" dy="40" class="call-to-action-text">立即行动，开启合作！</tspan>
    </text>
  </g>

  <!-- Logo和页码 -->
  <g>
    <!-- Logo占位符 -->
    <image href="{logo_url}" x="80" y="990" width="120" height="60" preserveAspectRatio="xMidYMid meet"/>
    <!-- 页码 -->
    <text x="1840" y="1020" text-anchor="end" class="small-text">10/10</text>
  </g>

</svg>